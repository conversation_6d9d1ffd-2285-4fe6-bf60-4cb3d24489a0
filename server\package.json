{"name": "igcse-student-guide-server", "version": "1.0.0", "description": "Backend proxy server for IGCSE Student Guide", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@supabase/supabase-js": "^2.50.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "jsonwebtoken": "^9.0.2", "openai": "^5.5.1"}, "devDependencies": {"nodemon": "^3.0.1"}}