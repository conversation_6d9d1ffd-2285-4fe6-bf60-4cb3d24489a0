import React, { useState, useEffect } from 'react';
import { useQuizGeneration } from '../../hooks/useLLMGeneration';
import { Subject } from '../../hooks/useSubjects';
import { Topic } from '../../hooks/useTopics';
import { LLMProvider } from '../../services/llmAdapter';
import LLMProviderSelector from './LLMProviderSelector';

interface QuizGeneratorFormProps {
  subjects: Subject[];
  topics: Topic[];
  onSubjectChange: (subjectId: string | null) => void;
}

/**
 * Form for generating and saving quizzes using LLM
 */
const QuizGeneratorForm: React.FC<QuizGeneratorFormProps> = ({ 
  subjects, 
  topics, 
  onSubjectChange 
}) => {
  const [selectedSubject, setSelectedSubject] = useState<Subject | null>(null);
  const [selectedTopic, setSelectedTopic] = useState<Topic | null>(null);
  const [questionCount, setQuestionCount] = useState<number>(10);
  const [generatedQuiz, setGeneratedQuiz] = useState<any | null>(null);
  const [savedQuiz, setSavedQuiz] = useState<any | null>(null);
  const [generating, setGenerating] = useState<boolean>(false);
  const [saving, setSaving] = useState<boolean>(false);
  const [showSuccess, setShowSuccess] = useState<boolean>(false);

  // LLM Provider state
  const [selectedProvider, setSelectedProvider] = useState<LLMProvider>(LLMProvider.OPENAI);
  const [selectedModel, setSelectedModel] = useState<string>('gpt-4o');

  const { generateQuiz, saveQuiz, loading, error } = useQuizGeneration();

  // Update selected subject when subjects load
  useEffect(() => {
    if (subjects.length > 0 && !selectedSubject) {
      setSelectedSubject(subjects[0]);
      onSubjectChange(subjects[0].id);
    }
  }, [subjects, selectedSubject, onSubjectChange]);

  // Update selected topic when topics load
  useEffect(() => {
    if (topics.length > 0 && !selectedTopic) {
      setSelectedTopic(topics[0]);
    }
  }, [topics, selectedTopic]);

  // Handle subject change
  const handleSubjectChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const subjectId = e.target.value;
    const subject = subjects.find(s => s.id === subjectId) || null;
    setSelectedSubject(subject);
    onSubjectChange(subjectId);
    setSelectedTopic(null); // Reset selected topic when subject changes
  };

  // Handle topic change
  const handleTopicChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const topicId = e.target.value;
    const topic = topics.find(t => t.id === topicId) || null;
    setSelectedTopic(topic);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedTopic) return;
    
    setGenerating(true);
    try {
      const result = await generateQuiz(
        selectedTopic.title,
        selectedTopic.content || '',
        questionCount,
        selectedProvider,
        selectedModel
      );
      setGeneratedQuiz(result);
    } catch (err) {
      console.error('Error generating quiz:', err);
    } finally {
      setGenerating(false);
    }
  };

  // Handle saving the generated quiz
  const handleSave = async () => {
    if (!selectedTopic || !generatedQuiz) return;
    
    setSaving(true);
    try {
      const result = await saveQuiz(selectedTopic.id, generatedQuiz);
      if (result) {
        setSavedQuiz(result);
        setShowSuccess(true);
        setTimeout(() => setShowSuccess(false), 5000);
      }
    } catch (err) {
      console.error('Error saving quiz:', err);
    } finally {
      setSaving(false);
    }
  };

  // Handle editing quiz metadata
  const handleEditQuiz = (field: string, value: any) => {
    if (!generatedQuiz) return;
    
    setGeneratedQuiz({
      ...generatedQuiz,
      [field]: value
    });
  };

  // Handle editing a question
  const handleEditQuestion = (index: number, field: string, value: any) => {
    if (!generatedQuiz) return;
    
    const updatedQuestions = [...generatedQuiz.questions];
    updatedQuestions[index] = {
      ...updatedQuestions[index],
      [field]: value
    };
    
    setGeneratedQuiz({
      ...generatedQuiz,
      questions: updatedQuestions
    });
  };

  // Handle editing a question option
  const handleEditOption = (questionIndex: number, optionIndex: number, value: string) => {
    if (!generatedQuiz) return;
    
    const updatedQuestions = [...generatedQuiz.questions];
    const updatedOptions = [...updatedQuestions[questionIndex].options];
    updatedOptions[optionIndex] = value;
    
    updatedQuestions[questionIndex] = {
      ...updatedQuestions[questionIndex],
      options: updatedOptions
    };
    
    setGeneratedQuiz({
      ...generatedQuiz,
      questions: updatedQuestions
    });
  };

  // Reset the form
  const handleReset = () => {
    setGeneratedQuiz(null);
    setSavedQuiz(null);
    setQuestionCount(10);
  };

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">Generate Quiz</h2>
      
      {/* Input form */}
      <form onSubmit={handleSubmit} className="mb-6">
        {/* LLM Provider Selection */}
        <div className="mb-6">
          <LLMProviderSelector
            selectedProvider={selectedProvider}
            selectedModel={selectedModel}
            onProviderChange={setSelectedProvider}
            onModelChange={setSelectedModel}
            disabled={generating || loading}
          />
        </div>

        <div className="mb-4">
          <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-1">
            Select Subject:
          </label>
          <select
            id="subject"
            value={selectedSubject?.id || ''}
            onChange={handleSubjectChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            disabled={generating || loading}
          >
            {subjects.map(subject => (
              <option key={subject.id} value={subject.id}>
                {subject.name}
              </option>
            ))}
          </select>
        </div>
        
        <div className="mb-4">
          <label htmlFor="topic" className="block text-sm font-medium text-gray-700 mb-1">
            Select Topic:
          </label>
          <select
            id="topic"
            value={selectedTopic?.id || ''}
            onChange={handleTopicChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            disabled={generating || loading || topics.length === 0}
          >
            {topics.map(topic => (
              <option key={topic.id} value={topic.id}>
                {topic.title}
              </option>
            ))}
          </select>
          {topics.length === 0 && (
            <p className="mt-1 text-sm text-red-600">
              No topics available for this subject. Please create topics first.
            </p>
          )}
        </div>
        
        <div className="mb-4">
          <label htmlFor="questionCount" className="block text-sm font-medium text-gray-700 mb-1">
            Number of Questions:
          </label>
          <input
            id="questionCount"
            type="number"
            min={5}
            max={20}
            value={questionCount}
            onChange={(e) => setQuestionCount(parseInt(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            disabled={generating || loading}
          />
        </div>
        
        <div className="flex gap-2">
          <button
            type="submit"
            disabled={generating || loading || !selectedTopic}
            className={`px-4 py-2 rounded-md text-white ${
              generating || loading || !selectedTopic
                ? 'bg-blue-300'
                : 'bg-blue-600 hover:bg-blue-700'
            } transition-colors`}
          >
            {generating || loading ? 'Generating...' : 'Generate Quiz'}
          </button>
          
          <button
            type="button"
            onClick={handleReset}
            className="px-4 py-2 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors"
          >
            Reset
          </button>
        </div>
      </form>
      
      {/* Error message */}
      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}
      
      {/* Generated quiz preview */}
      {generatedQuiz && (
        <div className="border rounded-md p-4 mb-6">
          <h3 className="text-lg font-medium mb-4">Generated Quiz</h3>
          
          <div className="space-y-6">
            {/* Quiz metadata */}
            <div className="space-y-4 border-b pb-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Title:</label>
                <input
                  type="text"
                  value={generatedQuiz.title}
                  onChange={(e) => handleEditQuiz('title', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Description:</label>
                <textarea
                  value={generatedQuiz.description}
                  onChange={(e) => handleEditQuiz('description', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  rows={2}
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Difficulty Level (1-5):</label>
                  <input
                    type="number"
                    min={1}
                    max={5}
                    value={generatedQuiz.difficulty_level}
                    onChange={(e) => handleEditQuiz('difficulty_level', parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Time Limit (minutes):</label>
                  <input
                    type="number"
                    min={1}
                    value={generatedQuiz.time_limit_minutes}
                    onChange={(e) => handleEditQuiz('time_limit_minutes', parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
            </div>
            
            {/* Questions */}
            <div>
              <h4 className="text-md font-medium mb-3">Questions</h4>
              
              <div className="space-y-6">
                {generatedQuiz.questions.map((question: any, qIndex: number) => (
                  <div key={qIndex} className="border p-3 rounded-md bg-gray-50">
                    <div className="font-medium text-gray-700 mb-2">Question #{qIndex + 1}</div>
                    
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Question Text:</label>
                        <textarea
                          value={question.question_text}
                          onChange={(e) => handleEditQuestion(qIndex, 'question_text', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          rows={2}
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Options:</label>
                        <div className="space-y-2">
                          {question.options.map((option: string, oIndex: number) => (
                            <div key={oIndex} className="flex items-center">
                              <input
                                type="radio"
                                checked={question.correct_answer_index === oIndex}
                                onChange={() => handleEditQuestion(qIndex, 'correct_answer_index', oIndex)}
                                className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500"
                              />
                              <input
                                type="text"
                                value={option}
                                onChange={(e) => handleEditOption(qIndex, oIndex, e.target.value)}
                                className="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                              />
                            </div>
                          ))}
                        </div>
                        <p className="mt-1 text-sm text-gray-500">
                          Select the radio button next to the correct answer.
                        </p>
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Explanation:</label>
                        <textarea
                          value={question.explanation}
                          onChange={(e) => handleEditQuestion(qIndex, 'explanation', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          rows={2}
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Difficulty Level (1-5):</label>
                        <input
                          type="number"
                          min={1}
                          max={5}
                          value={question.difficulty_level}
                          onChange={(e) => handleEditQuestion(qIndex, 'difficulty_level', parseInt(e.target.value))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            
            <button
              onClick={handleSave}
              disabled={saving || loading}
              className={`px-4 py-2 rounded-md text-white ${
                saving || loading
                  ? 'bg-green-300'
                  : 'bg-green-600 hover:bg-green-700'
              } transition-colors`}
            >
              {saving || loading ? 'Saving...' : 'Save Quiz'}
            </button>
          </div>
        </div>
      )}
      
      {/* Success message */}
      {showSuccess && savedQuiz && (
        <div className="mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded">
          Quiz "{savedQuiz.quiz.title}" with {savedQuiz.questions.length} questions has been successfully saved!
        </div>
      )}
    </div>
  );
};

export default QuizGeneratorForm;
