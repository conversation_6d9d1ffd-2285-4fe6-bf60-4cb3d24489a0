import React, { useState, useEffect } from 'react';
import { useFlashcardGeneration } from '../../hooks/useLLMGeneration';
import { useAuth } from '../../contexts/AuthContext';
import { Subject } from '../../hooks/useSubjects';
import { Topic } from '../../hooks/useTopics';
import { Flashcard } from '../../hooks/useFlashcards';
import { checkAuthStatus, testApiConnectivity, getAdminSetupInstructions } from '../../utils/adminSetup';
import { LLMProvider } from '../../services/llmAdapter';
import LLMProviderSelector from './LLMProviderSelector';

interface FlashcardGeneratorFormProps {
  subjects: Subject[];
  topics: Topic[];
  onSubjectChange: (subjectId: string | null) => void;
}

/**
 * Form for generating and saving flashcards using LLM
 */
const FlashcardGeneratorForm: React.FC<FlashcardGeneratorFormProps> = ({ 
  subjects, 
  topics, 
  onSubjectChange 
}) => {
  const [selectedSubject, setSelectedSubject] = useState<Subject | null>(null);
  const [selectedTopic, setSelectedTopic] = useState<Topic | null>(null);
  const [cardCount, setCardCount] = useState<number>(5);
  const [generatedFlashcards, setGeneratedFlashcards] = useState<Partial<Flashcard>[] | null>(null);
  const [savedFlashcards, setSavedFlashcards] = useState<Flashcard[] | null>(null);
  const [generating, setGenerating] = useState<boolean>(false);
  const [saving, setSaving] = useState<boolean>(false);
  const [showSuccess, setShowSuccess] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  // LLM Provider state
  const [selectedProvider, setSelectedProvider] = useState<LLMProvider>(LLMProvider.OPENAI);
  const [selectedModel, setSelectedModel] = useState<string>('gpt-4o');

  const { generateFlashcards, saveFlashcards, loading, error } = useFlashcardGeneration();

  // Add authentication debugging
  const { session, isAdmin, user } = useAuth();

  // Debug authentication status
  useEffect(() => {
    console.log('🔐 Authentication Debug:', {
      hasSession: !!session,
      hasUser: !!user,
      isAdmin,
      userEmail: user?.email,
      userRole: user?.app_metadata?.role,
      accessToken: session?.access_token ? 'Present' : 'Missing'
    });
  }, [session, isAdmin, user]);

  // Update selected subject when subjects load
  useEffect(() => {
    if (subjects.length > 0 && !selectedSubject) {
      setSelectedSubject(subjects[0]);
      onSubjectChange(subjects[0].id);
    }
  }, [subjects, selectedSubject, onSubjectChange]);

  // Update selected topic when topics load
  useEffect(() => {
    if (topics.length > 0 && !selectedTopic) {
      setSelectedTopic(topics[0]);
    }
  }, [topics, selectedTopic]);

  // Clear error message when selection changes
  useEffect(() => {
    if (errorMessage) {
      setErrorMessage(null);
    }
  }, [selectedSubject, selectedTopic]);

  // Update error message when hook error changes
  useEffect(() => {
    if (error) {
      setErrorMessage(error);
    }
  }, [error]);

  // Debug: Track generatedFlashcards state changes
  useEffect(() => {
    console.log('🔄 generatedFlashcards state changed:', {
      hasFlashcards: !!generatedFlashcards,
      count: generatedFlashcards?.length || 0,
      firstCard: generatedFlashcards?.[0] || null
    });
  }, [generatedFlashcards]);

  // Handle subject change
  const handleSubjectChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const subjectId = e.target.value;
    const subject = subjects.find(s => s.id === subjectId) || null;
    setSelectedSubject(subject);
    onSubjectChange(subjectId);
    setSelectedTopic(null); // Reset selected topic when subject changes
  };

  // Handle topic change
  const handleTopicChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const topicId = e.target.value;
    const topic = topics.find(t => t.id === topicId) || null;
    setSelectedTopic(topic);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedTopic) {
      console.error('❌ Cannot submit: No topic selected');
      setErrorMessage('Please select a topic before generating flashcards.');
      return;
    }

    // Check authentication before proceeding
    if (!session || !session.access_token) {
      console.error('❌ Cannot submit: No authentication session');
      setErrorMessage('Authentication required. Please log in to generate flashcards.');
      return;
    }

    if (!isAdmin) {
      console.error('❌ Cannot submit: User is not admin');
      setErrorMessage('Admin privileges required to generate flashcards.');
      return;
    }

    console.log('🚀 Starting flashcard generation...');
    console.log('📋 Selected subject:', selectedSubject);
    console.log('📋 Selected topic before generation:', selectedTopic);
    console.log('📋 Selected topic ID:', selectedTopic.id);
    console.log('📋 Selected topic title:', selectedTopic.title);
    console.log('📋 Selected topic content length:', selectedTopic.content?.length || 0);
    console.log('📋 Card count:', cardCount);
    console.log('🔐 Auth token available:', !!session.access_token);

    // Clear any previous errors
    setErrorMessage(null);
    setGenerating(true);

    try {
      console.log('🔄 Calling generateFlashcards...');
      const result = await generateFlashcards(
        selectedTopic.title,
        selectedTopic.content || '',
        cardCount,
        selectedProvider,
        selectedModel
      );

      console.log('📦 Raw generation result:', result);
      console.log('📦 Result type:', typeof result);
      console.log('📦 Is array:', Array.isArray(result));
      console.log('📦 Result keys:', result && typeof result === 'object' ? Object.keys(result) : 'N/A');

      if (result) {
        // Check if result is an array (as expected by the type)
        if (Array.isArray(result)) {
          console.log('✅ Processing direct array result with', result.length, 'items');
          console.log('📋 First flashcard:', result[0]);
          setGeneratedFlashcards(result);
          console.log('✅ State updated with flashcards array');
        }
        // Handle case where API might return {flashcards: [...]} format
        else if (typeof result === 'object' && result !== null && 'flashcards' in result) {
          const flashcardsResult = (result as any).flashcards;
          console.log('📦 Found flashcards property:', flashcardsResult);
          console.log('📦 Flashcards is array:', Array.isArray(flashcardsResult));

          if (Array.isArray(flashcardsResult)) {
            console.log('✅ Processing wrapped flashcards result with', flashcardsResult.length, 'items');
            console.log('📋 First flashcard:', flashcardsResult[0]);
            setGeneratedFlashcards(flashcardsResult);
            console.log('✅ State updated with flashcards from wrapped object');
          } else {
            console.error('❌ Flashcards property is not an array:', flashcardsResult);
            setErrorMessage('Received an unexpected response format from the server');
          }
        }
        else {
          console.error('❌ Unexpected flashcard result format:', result);
          console.error('❌ Expected array or object with flashcards property');
          setErrorMessage('Received an unexpected response format from the server');
        }
      } else {
        console.error('❌ No flashcards returned from generation (result is null/undefined)');
        setErrorMessage('Failed to generate flashcards. Please try again.');
      }
    } catch (err) {
      console.error('❌ Exception during flashcard generation:', err);
      setErrorMessage(err instanceof Error ? err.message : 'Failed to generate flashcards');
    } finally {
      setGenerating(false);
      console.log('🏁 Generation process completed');
    }
  };

  // Handle saving the generated flashcards
  const handleSave = async () => {
    if (!selectedTopic || !generatedFlashcards) {
      setErrorMessage("No flashcards to save or topic not selected");
      console.error('Cannot save: No flashcards or topic not selected', { 
        hasSelectedTopic: !!selectedTopic, 
        selectedTopicId: selectedTopic?.id,
        hasGeneratedFlashcards: !!generatedFlashcards,
        flashcardsCount: generatedFlashcards?.length
      });
      return;
    }
    
    // Validate flashcards before saving
    const invalidFlashcards = generatedFlashcards.filter(card => 
      !card.front_content || !card.back_content
    );
    
    if (invalidFlashcards.length > 0) {
      setErrorMessage("Some flashcards are missing front or back content. Please complete all fields.");
      console.error('Cannot save: Invalid flashcards found', { invalidFlashcards });
      return;
    }
    
    setSaving(true);
    setErrorMessage(null);
    
    console.log('Starting flashcard save process', { 
      topicId: selectedTopic.id,
      topicTitle: selectedTopic.title,
      flashcardsCount: generatedFlashcards.length,
      firstFlashcard: generatedFlashcards[0]
    });
    
    try {
      console.log('Calling saveFlashcards with topic ID:', selectedTopic.id);
      const result = await saveFlashcards(selectedTopic.id, generatedFlashcards);
      console.log('saveFlashcards result:', result);
      
      if (result) {
        setSavedFlashcards(result);
        setShowSuccess(true);
        console.log('Flashcards saved successfully:', result);
        setTimeout(() => setShowSuccess(false), 5000);
      } else {
        console.error('saveFlashcards returned null/undefined', { error });
        if (error) {
          setErrorMessage(error);
          console.error('Error from hook:', error);
        } else {
          setErrorMessage('Failed to save flashcards: Unknown error');
          console.error('No error from hook but save failed');
        }
      }
    } catch (err) {
      console.error('Exception while saving flashcards:', err);
      setErrorMessage(err instanceof Error ? err.message : 'Failed to save flashcards');
    } finally {
      setSaving(false);
    }
  };

  // Handle editing a flashcard
  const handleEditFlashcard = (index: number, field: string, value: any) => {
    if (!generatedFlashcards) return;
    
    const updatedFlashcards = [...generatedFlashcards];
    updatedFlashcards[index] = {
      ...updatedFlashcards[index],
      [field]: value
    };
    
    setGeneratedFlashcards(updatedFlashcards);
  };

  // Reset the form
  const handleReset = () => {
    setGeneratedFlashcards(null);
    setSavedFlashcards(null);
    setCardCount(5);
  };

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">Generate Flashcards</h2>
      
      {/* Input form */}
      <form onSubmit={handleSubmit} className="mb-6">
        {/* LLM Provider Selection */}
        <div className="mb-6">
          <LLMProviderSelector
            selectedProvider={selectedProvider}
            selectedModel={selectedModel}
            onProviderChange={setSelectedProvider}
            onModelChange={setSelectedModel}
            disabled={generating || loading}
          />
        </div>

        <div className="mb-4">
          <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-1">
            Select Subject:
          </label>
          <select
            id="subject"
            value={selectedSubject?.id || ''}
            onChange={handleSubjectChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            disabled={generating || loading}
          >
            {subjects.map(subject => (
              <option key={subject.id} value={subject.id}>
                {subject.name}
              </option>
            ))}
          </select>
        </div>
        
        <div className="mb-4">
          <label htmlFor="topic" className="block text-sm font-medium text-gray-700 mb-1">
            Select Topic:
          </label>
          <select
            id="topic"
            value={selectedTopic?.id || ''}
            onChange={handleTopicChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            disabled={generating || loading || topics.length === 0}
          >
            {topics.map(topic => (
              <option key={topic.id} value={topic.id}>
                {topic.title}
              </option>
            ))}
          </select>
          {topics.length === 0 && (
            <p className="mt-1 text-sm text-red-600">
              No topics available for this subject. Please create topics first.
            </p>
          )}
        </div>
        
        <div className="mb-4">
          <label htmlFor="cardCount" className="block text-sm font-medium text-gray-700 mb-1">
            Number of Flashcards:
          </label>
          <input
            id="cardCount"
            type="number"
            min={1}
            max={20}
            value={cardCount}
            onChange={(e) => setCardCount(parseInt(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            disabled={generating || loading}
          />
        </div>
        
        <div className="flex gap-2">
          <button
            type="submit"
            disabled={generating || loading || !selectedTopic}
            className={`px-4 py-2 rounded-md text-white ${
              generating || loading || !selectedTopic
                ? 'bg-blue-300'
                : 'bg-blue-600 hover:bg-blue-700'
            } transition-colors`}
          >
            {generating || loading ? 'Generating...' : 'Generate Flashcards'}
          </button>
          
          <button
            type="button"
            onClick={handleReset}
            className="px-4 py-2 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors"
          >
            Reset
          </button>

          <button
            type="button"
            onClick={async () => {
              const result = await checkAuthStatus();
              console.log('🔍 Auth Status Result:', result);
              if (!result.isAdmin) {
                console.log(getAdminSetupInstructions());
              }
            }}
            className="px-4 py-2 bg-purple-200 rounded-md hover:bg-purple-300 transition-colors text-sm"
          >
            Debug Auth
          </button>

          <button
            type="button"
            onClick={async () => {
              const result = await testApiConnectivity();
              console.log('🧪 API Test Result:', result);
            }}
            className="px-4 py-2 bg-yellow-200 rounded-md hover:bg-yellow-300 transition-colors text-sm"
          >
            Test API
          </button>
        </div>
      </form>
      
      {/* Error message */}
      {errorMessage && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {errorMessage}
        </div>
      )}
      
      {/* Authentication status */}
      <div className={`mb-4 p-3 border rounded text-sm ${
        session && isAdmin
          ? 'bg-green-50 border-green-200 text-green-800'
          : 'bg-yellow-50 border-yellow-200 text-yellow-800'
      }`}>
        <strong>Authentication Status:</strong>
        {session ? (
          <>
            ✅ Logged in as {user?.email} |
            {isAdmin ? ' ✅ Admin privileges' : ' ❌ No admin privileges'} |
            Token: {session.access_token ? 'Present' : 'Missing'}
          </>
        ) : (
          ' ❌ Not authenticated'
        )}

        {session && !isAdmin && (
          <div className="mt-2 p-2 bg-yellow-100 border border-yellow-300 rounded">
            <strong>⚠️ Admin Setup Required:</strong>
            <p className="mt-1 text-xs">
              To generate flashcards, you need admin privileges.
              Click "Debug Auth" for setup instructions, or contact an administrator.
            </p>
          </div>
        )}
      </div>

      {/* Debug info */}
      <div className="mb-4 p-3 bg-gray-100 border border-gray-300 text-gray-700 rounded text-sm">
        <strong>Debug Info:</strong>
        generatedFlashcards: {generatedFlashcards ? `Array(${generatedFlashcards.length})` : 'null'} |
        generating: {generating.toString()} |
        loading: {loading.toString()} |
        error: {error || 'none'}
      </div>

      {/* Generated flashcards preview */}
      {(() => {
        console.log('🎨 Rendering flashcards section. Conditions:', {
          hasGeneratedFlashcards: !!generatedFlashcards,
          flashcardsLength: generatedFlashcards?.length || 0,
          conditionMet: generatedFlashcards && generatedFlashcards.length > 0
        });
        return generatedFlashcards && generatedFlashcards.length > 0;
      })() && (
        <div className="border rounded-md p-4 mb-6">
          <h3 className="text-lg font-medium mb-4">Generated Flashcards ({generatedFlashcards?.length || 0})</h3>

          <div className="space-y-6">
            {generatedFlashcards?.map((flashcard, index) => (
              <div key={index} className="border p-3 rounded-md bg-gray-50">
                <div className="font-medium text-gray-700 mb-2">Flashcard #{index + 1}</div>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Front Content:</label>
                    <textarea
                      value={flashcard.front_content || ''}
                      onChange={(e) => handleEditFlashcard(index, 'front_content', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      rows={2}
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Back Content:</label>
                    <textarea
                      value={flashcard.back_content || ''}
                      onChange={(e) => handleEditFlashcard(index, 'back_content', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      rows={3}
                    />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Card Type:</label>
                      <select
                        value={flashcard.card_type || 'basic'}
                        onChange={(e) => handleEditFlashcard(index, 'card_type', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="basic">Basic</option>
                        <option value="cloze">Cloze</option>
                        <option value="multiple_choice">Multiple Choice</option>
                      </select>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Difficulty Level (1-5):</label>
                      <input
                        type="number"
                        min={1}
                        max={5}
                        value={flashcard.difficulty_level || 3}
                        onChange={(e) => handleEditFlashcard(index, 'difficulty_level', parseInt(e.target.value))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Hint (optional):</label>
                    <input
                      type="text"
                      value={flashcard.hint || ''}
                      onChange={(e) => handleEditFlashcard(index, 'hint', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Explanation (optional):</label>
                    <textarea
                      value={flashcard.explanation || ''}
                      onChange={(e) => handleEditFlashcard(index, 'explanation', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      rows={2}
                    />
                  </div>
                </div>
              </div>
            ))}
            
            <button
              onClick={handleSave}
              disabled={saving || loading}
              className={`px-4 py-2 rounded-md text-white ${
                saving || loading
                  ? 'bg-green-300'
                  : 'bg-green-600 hover:bg-green-700'
              } transition-colors`}
            >
              {saving || loading ? 'Saving...' : 'Save All Flashcards'}
            </button>
          </div>
        </div>
      )}
      
      {/* Success message */}
      {showSuccess && savedFlashcards && (
        <div className="mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded">
          {savedFlashcards.length} flashcards have been successfully saved!
        </div>
      )}
    </div>
  );
};

export default FlashcardGeneratorForm;
