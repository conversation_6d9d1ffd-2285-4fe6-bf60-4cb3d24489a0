import React, { useState, useEffect } from 'react';
import { useTopicGeneration } from '../../hooks/useLLMGeneration';
import { Subject } from '../../hooks/useSubjects';
import { Topic } from '../../hooks/useTopics';
import { LLMProvider } from '../../services/llmAdapter';
import LLMProviderSelector from './LLMProviderSelector';

interface TopicGeneratorFormProps {
  subjects: Subject[];
  onSubjectChange: (subjectId: string | null) => void;
}

/**
 * Form for generating and saving topics using LLM
 */
const TopicGeneratorForm: React.FC<TopicGeneratorFormProps> = ({ subjects, onSubjectChange }) => {
  const [selectedSubject, setSelectedSubject] = useState<Subject | null>(null);
  const [topicName, setTopicName] = useState<string>('');
  const [generatedTopic, setGeneratedTopic] = useState<any | null>(null);
  const [savedTopic, setSavedTopic] = useState<Topic | null>(null);
  const [generating, setGenerating] = useState<boolean>(false);
  const [saving, setSaving] = useState<boolean>(false);
  const [showSuccess, setShowSuccess] = useState<boolean>(false);

  // LLM Provider state
  const [selectedProvider, setSelectedProvider] = useState<LLMProvider>(LLMProvider.OPENAI);
  const [selectedModel, setSelectedModel] = useState<string>('gpt-4o');
  
  const { generateTopic, saveTopic, loading, error } = useTopicGeneration();

  // Update selected subject when subjects load
  useEffect(() => {
    console.log('Subjects changed:', subjects);
    console.log('Current selectedSubject:', selectedSubject);
    
    if (subjects.length > 0 && !selectedSubject) {
      console.log('Setting initial subject:', subjects[0]);
      setSelectedSubject(subjects[0]);
      onSubjectChange(subjects[0].id);
    } else if (subjects.length === 0) {
      // Reset selected subject if no subjects are available
      console.log('No subjects available, resetting selection');
      setSelectedSubject(null);
      onSubjectChange(null);
    }
  }, [subjects, selectedSubject, onSubjectChange]);

  // Handle subject change
  const handleSubjectChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const subjectId = e.target.value;
    console.log('Subject selected from dropdown:', subjectId);
    const subject = subjects.find(s => s.id === subjectId) || null;
    console.log('Found subject object:', subject);
    setSelectedSubject(subject);
    onSubjectChange(subjectId);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedSubject || !topicName.trim()) return;
    
    setGenerating(true);
    try {
      const result = await generateTopic(selectedSubject.name, topicName, selectedProvider, selectedModel);
      setGeneratedTopic(result);
    } catch (err) {
      console.error('Error generating topic:', err);
    } finally {
      setGenerating(false);
    }
  };

  // Handle saving the generated topic
  const handleSave = async () => {
    if (!selectedSubject || !generatedTopic) return;
    
    console.log('Saving topic for subject:', selectedSubject);
    console.log('Subject ID being used:', selectedSubject.id);
    
    setSaving(true);
    try {
      const result = await saveTopic(selectedSubject.id, generatedTopic);
      if (result) {
        setSavedTopic(result);
        setShowSuccess(true);
        setTimeout(() => setShowSuccess(false), 5000);
      }
    } catch (err) {
      console.error('Error saving topic:', err);
    } finally {
      setSaving(false);
    }
  };

  // Handle editing the generated topic
  const handleEdit = (field: string, value: any) => {
    if (!generatedTopic) return;
    
    setGeneratedTopic({
      ...generatedTopic,
      [field]: value
    });
  };

  // Reset the form
  const handleReset = () => {
    setTopicName('');
    setGeneratedTopic(null);
    setSavedTopic(null);
  };

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">Generate New Topic</h2>
      
      {/* Input form */}
      <form onSubmit={handleSubmit} className="mb-6">
        {/* LLM Provider Selection */}
        <div className="mb-6">
          <LLMProviderSelector
            selectedProvider={selectedProvider}
            selectedModel={selectedModel}
            onProviderChange={setSelectedProvider}
            onModelChange={setSelectedModel}
            disabled={generating || loading}
          />
        </div>

        <div className="mb-4">
          <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-1">
            Select Subject:
          </label>
          {subjects.length > 0 ? (
            <select
              id="subject"
              value={selectedSubject?.id || ''}
              onChange={handleSubjectChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              disabled={generating || loading}
            >
              {subjects.map(subject => (
                <option key={subject.id} value={subject.id}>
                  {subject.name}
                </option>
              ))}
            </select>
          ) : (
            <div className="p-3 bg-yellow-100 border border-yellow-400 text-yellow-700 rounded">
              No subjects available. Please create a subject first before generating topics.
            </div>
          )}
        </div>
        
        <div className="mb-4">
          <label htmlFor="topicName" className="block text-sm font-medium text-gray-700 mb-1">
            Topic Name:
          </label>
          <input
            id="topicName"
            type="text"
            value={topicName}
            onChange={(e) => setTopicName(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            disabled={generating || loading || subjects.length === 0}
            placeholder={subjects.length === 0 ? "Create a subject first" : "Enter topic name"}
          />
        </div>
        
        <div className="flex gap-2">
          <button
            type="submit"
            disabled={generating || loading || !topicName.trim() || subjects.length === 0 || !selectedSubject}
            className={`px-4 py-2 rounded-md text-white ${
              generating || loading || !topicName.trim() || subjects.length === 0 || !selectedSubject
                ? 'bg-blue-300 cursor-not-allowed'
                : 'bg-blue-600 hover:bg-blue-700'
            } transition-colors`}
          >
            {generating || loading ? 'Generating...' : 'Generate Topic'}
          </button>
          
          <button
            type="button"
            onClick={handleReset}
            className="px-4 py-2 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors"
          >
            Reset
          </button>
        </div>
      </form>
      
      {/* Error message */}
      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}
      
      {/* Generated topic preview */}
      {generatedTopic && (
        <div className="border rounded-md p-4 mb-6">
          <h3 className="text-lg font-medium mb-4">Generated Topic</h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Title:</label>
              <input
                type="text"
                value={generatedTopic.title}
                onChange={(e) => handleEdit('title', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Description:</label>
              <textarea
                value={generatedTopic.description}
                onChange={(e) => handleEdit('description', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                rows={2}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Content:</label>
              <textarea
                value={generatedTopic.content}
                onChange={(e) => handleEdit('content', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                rows={10}
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Difficulty Level (1-5):</label>
                <input
                  type="number"
                  min={1}
                  max={5}
                  value={generatedTopic.difficulty_level}
                  onChange={(e) => handleEdit('difficulty_level', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Estimated Study Time (minutes):</label>
                <input
                  type="number"
                  min={1}
                  value={generatedTopic.estimated_study_time_minutes}
                  onChange={(e) => handleEdit('estimated_study_time_minutes', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Learning Objectives:</label>
              <div className="space-y-2">
                {generatedTopic.learning_objectives.map((objective: string, index: number) => (
                  <input
                    key={index}
                    type="text"
                    value={objective}
                    onChange={(e) => {
                      const newObjectives = [...generatedTopic.learning_objectives];
                      newObjectives[index] = e.target.value;
                      handleEdit('learning_objectives', newObjectives);
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                ))}
                <button
                  type="button"
                  onClick={() => {
                    const newObjectives = [...generatedTopic.learning_objectives, ''];
                    handleEdit('learning_objectives', newObjectives);
                  }}
                  className="px-3 py-1 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors text-sm"
                >
                  Add Objective
                </button>
              </div>
            </div>
            
            <button
              onClick={handleSave}
              disabled={saving || loading}
              className={`px-4 py-2 rounded-md text-white ${
                saving || loading
                  ? 'bg-green-300'
                  : 'bg-green-600 hover:bg-green-700'
              } transition-colors`}
            >
              {saving || loading ? 'Saving...' : 'Save Topic'}
            </button>
          </div>
        </div>
      )}
      
      {/* Success message */}
      {showSuccess && savedTopic && (
        <div className="mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded">
          Topic "{savedTopic.title}" has been successfully saved!
        </div>
      )}
      
      {/* Example topic suggestions */}
      <div className="mt-8">
        <h3 className="text-lg font-medium mb-2">Example Topic Names</h3>
        <div className="space-y-2">
          <button
            onClick={() => setTopicName('Cell Structure and Function')}
            className="block w-full text-left px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
          >
            Cell Structure and Function
          </button>
          <button
            onClick={() => setTopicName('Forces and Motion')}
            className="block w-full text-left px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
          >
            Forces and Motion
          </button>
          <button
            onClick={() => setTopicName('Algebraic Expressions')}
            className="block w-full text-left px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
          >
            Algebraic Expressions
          </button>
        </div>
      </div>
    </div>
  );
};

export default TopicGeneratorForm;
