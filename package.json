{"name": "igcse-student-guide", "version": "0.1.0", "private": true, "description": "A comprehensive learning platform for IGCSE Grade 9-10 students", "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/material": "^7.1.1", "@supabase/auth-helpers-react": "^0.4.2", "@supabase/auth-ui-react": "^0.4.6", "@supabase/supabase-js": "^2.38.4", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.68", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "framer-motion": "^12.18.1", "node-fetch": "^3.3.2", "openai": "^5.5.1", "react": "^18.2.0", "react-card-flip": "^1.2.3", "react-dom": "^18.2.0", "react-markdown": "^10.1.0", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "recharts": "^2.15.3", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.3.6"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "dev": "react-scripts start"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "keywords": ["igcse", "education", "learning", "flashcards", "quiz", "student", "react", "typescript", "supabase"], "author": "IGCSE Student Guide Team", "license": "MIT"}