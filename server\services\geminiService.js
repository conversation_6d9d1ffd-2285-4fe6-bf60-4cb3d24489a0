/**
 * Google Gemini API Service
 * Handles text and JSON generation using Google's Generative AI
 */
const { GoogleGenerativeAI } = require('@google/generative-ai');
require('dotenv').config();

class GeminiService {
  constructor() {
    if (!process.env.GOOGLE_API_KEY || process.env.GOOGLE_API_KEY === 'your_google_api_key_here') {
      throw new Error('GOOGLE_API_KEY environment variable is required and must be a valid API key');
    }

    this.genAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY);
    this.isAvailable = true;
  }

  /**
   * Check if the service is properly configured
   */
  static isConfigured() {
    return !!(process.env.GOOGLE_API_KEY && process.env.GOOGLE_API_KEY !== 'your_google_api_key_here');
  }

  /**
   * Generate text content using Gemini
   * @param {string} prompt - The prompt to send to Gemini
   * @param {Object} options - Generation options
   * @returns {Promise<string>} Generated text
   */
  async generateText(prompt, options = {}) {
    try {
      const {
        model = 'gemini-1.5-flash',
        temperature = 0.7,
        maxTokens = 1000
      } = options;

      const geminiModel = this.genAI.getGenerativeModel({ 
        model,
        generationConfig: {
          temperature,
          maxOutputTokens: maxTokens,
        }
      });

      const result = await geminiModel.generateContent(prompt);
      const response = await result.response;
      return response.text();
    } catch (error) {
      console.error('Gemini API error:', error);
      throw new Error(`Gemini API error: ${error.message}`);
    }
  }

  /**
   * Generate JSON content using Gemini
   * @param {string} prompt - The prompt to send to Gemini
   * @param {Object} options - Generation options
   * @returns {Promise<Object>} Parsed JSON response
   */
  async generateJSON(prompt, options = {}) {
    try {
      const {
        model = 'gemini-1.5-flash',
        temperature = 0.7,
        maxTokens = 1000
      } = options;

      // Enhanced prompt for JSON generation
      const jsonPrompt = `${prompt}

IMPORTANT: You must respond with a valid, parseable JSON object only.
- No markdown formatting, backticks, or code blocks
- No explanatory text before or after the JSON
- Ensure all property names and string values are in double quotes
- No trailing commas
- Follow proper JSON syntax`;

      const geminiModel = this.genAI.getGenerativeModel({ 
        model,
        generationConfig: {
          temperature,
          maxOutputTokens: maxTokens,
        }
      });

      const result = await geminiModel.generateContent(jsonPrompt);
      const response = await result.response;
      let jsonText = response.text();

      // Clean up common JSON formatting issues
      jsonText = jsonText.trim();

      // Remove code block markers if present
      if (jsonText.startsWith('```json')) {
        jsonText = jsonText.replace(/^```json\n/, '').replace(/\n```$/, '');
      } else if (jsonText.startsWith('```')) {
        jsonText = jsonText.replace(/^```\n/, '').replace(/\n```$/, '');
      }

      // Additional cleanup for common JSON issues
      jsonText = this._cleanupJsonString(jsonText);

      // Parse and return JSON
      try {
        return JSON.parse(jsonText);
      } catch (parseError) {
        console.error('JSON parsing error:', parseError);
        console.error('Raw response (first 500 chars):', jsonText.substring(0, 500));
        console.error('Raw response (last 500 chars):', jsonText.substring(Math.max(0, jsonText.length - 500)));

        // Try to fix common issues and parse again
        const fixedJson = this._attemptJsonFix(jsonText);
        if (fixedJson) {
          console.log('✅ Successfully fixed JSON formatting');
          return fixedJson;
        }

        throw new Error(`Failed to parse JSON response: ${parseError.message}`);
      }
    } catch (error) {
      console.error('Gemini JSON generation error:', error);
      throw new Error(`Gemini JSON generation error: ${error.message}`);
    }
  }

  /**
   * Clean up common JSON string issues
   * @param {string} jsonText - The JSON text to clean
   * @returns {string} Cleaned JSON text
   */
  _cleanupJsonString(jsonText) {
    // Fix common escape sequence issues
    jsonText = jsonText
      .replace(/\\n/g, '\\n')  // Ensure newlines are properly escaped
      .replace(/\\"/g, '\\"')  // Ensure quotes are properly escaped
      .replace(/\\'/g, "'")    // Fix single quotes
      .replace(/[\u0000-\u001F\u007F-\u009F]/g, ''); // Remove control characters

    // Fix unterminated strings by finding and fixing quote issues
    const lines = jsonText.split('\n');
    const fixedLines = lines.map(line => {
      // If line has odd number of unescaped quotes, it might be unterminated
      const unescapedQuotes = (line.match(/(?<!\\)"/g) || []).length;
      if (unescapedQuotes % 2 !== 0 && line.trim().endsWith(',')) {
        // Try to fix by adding closing quote before comma
        return line.replace(/,$/, '",');
      }
      return line;
    });

    return fixedLines.join('\n');
  }

  /**
   * Attempt to fix common JSON formatting issues
   * @param {string} jsonText - The malformed JSON text
   * @returns {Object|null} Parsed JSON object or null if unfixable
   */
  _attemptJsonFix(jsonText) {
    const fixes = [
      // Fix trailing commas
      (text) => text.replace(/,(\s*[}\]])/g, '$1'),

      // Fix missing quotes around property names
      (text) => text.replace(/([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:/g, '$1"$2":'),

      // Fix unterminated strings at end of lines
      (text) => text.replace(/:\s*"([^"]*?)$/gm, ': "$1"'),

      // Fix double quotes in string values
      (text) => text.replace(/"([^"]*)"([^"]*)"([^"]*?)"/g, '"$1\\"$2\\"$3"'),

      // Remove any trailing text after the last }
      (text) => {
        const lastBrace = text.lastIndexOf('}');
        return lastBrace !== -1 ? text.substring(0, lastBrace + 1) : text;
      }
    ];

    for (const fix of fixes) {
      try {
        const fixedText = fix(jsonText);
        const parsed = JSON.parse(fixedText);
        return parsed;
      } catch (e) {
        // Continue to next fix
        continue;
      }
    }

    return null;
  }

  /**
   * Get available Gemini models
   * @returns {Array<string>} List of available model names
   */
  getAvailableModels() {
    return [
      'gemini-1.5-flash',
      'gemini-1.5-pro',
      'gemini-1.0-pro'
    ];
  }
}

module.exports = GeminiService;
